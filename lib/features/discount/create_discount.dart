import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../models/discounts.dart';
import '../../utils/db_stubs.dart' as stubs;
import '../../utils/db_stubs.dart';
import '../../utils/function_utils.dart';
import '../../utils/production_validation.dart';
import '../../database/app_database.dart';
import '../../database/dao/enhanced_discount_dao.dart';
import '../../widgets/common_appbar.dart';
import '../../widgets/common_drawer.dart';

// ignore: must_be_immutable
class CreateDiscount extends StatefulWidget {
  final Discounts? discounts;
  const CreateDiscount({super.key, this.discounts});
  @override
  State<CreateDiscount> createState() => _CreateDiscountState();
}

class _CreateDiscountState extends State<CreateDiscount> {
  Discounts? get discounts => widget.discounts;
  stubs.Validator validator = stubs.Validator();
  FnUtilities fnUtilities = FnUtilities();
  stubs.WorkspaceListResModel settingsDetail = stubs.WorkspaceListResModel();
  stubs.DiscountDB discountDB = stubs.DiscountDB();
  stubs.CommonDB commonDB = stubs.CommonDB();
  stubs.ProductKeyDBScript productKeyDBScript = stubs.ProductKeyDBScript();
  final couponNameController = TextEditingController();
  final discountController = TextEditingController();
  final minLimitController = TextEditingController();
  final maxLimitController = TextEditingController();
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  List<stubs.ProductKey> productKeyList = [];
  List<stubs.ProductKey> selectedProductKeyList = [];
  bool isLock = false;
  String? selectedDiscountType;
  String? selectedDiscountMode;
  String? selectedDiscountOn;

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return stubs.customTheme(
            child: child,
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    // if (picked != null && picked != fromDate && dateType == 'fromDate')
    //   setState(() {
    //     fromDate = picked;
    //   });
    // if (picked != null && picked != toDate && dateType == 'toDate')
    //   setState(() {
    //     toDate = picked;
    //   });
    if (picked != null) {
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          stubs.showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked;
          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          stubs.showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
          });
        }
      }
    }
  }

  //GET Key
  queryAllProductKeys() async {
    for (stubs.ProductKey item in stubs.ListUtility.productKeyList) {
      item.isActive = false;
      productKeyList.add(item);
    }
    await assignValueForUpdate();
  }

  clearTextFiled() {
    discountController.clear();
    minLimitController.clear();
    maxLimitController.clear();
    fromDate = DateTime.now();
    toDate = DateTime.now();
    selectedProductKeyList.clear();
    selectedDiscountType = null;
    selectedDiscountMode = null;
    selectedDiscountOn = null;
  }

  onPressDiscount() async {
    bool insertSuccess = false;
    bool updateSuccess = false;

    try {
      // Validate discount data before saving
      final validationResult = ProductionValidation.validateDiscount(
        discountType: selectedDiscountType == "Sales Wise" ? "S" : "P",
        discountMode: selectedDiscountMode == "Auto" ? "A" : "C",
        discountValue: discountController.text.trim(),
        minLimit: minLimitController.text.trim(),
        maxLimit: maxLimitController.text.trim().isNotEmpty ? maxLimitController.text.trim() : null,
        couponName: couponNameController.text.trim().isNotEmpty ? couponNameController.text.trim() : null,
        fromDate: fromDate.toString(),
        toDate: toDate.toString(),
      );

      if (!validationResult.isValid) {
        // Show validation errors to user
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Validation Error'),
            content: Text(validationResult.errorMessage),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
        );
        return;
      }

      Discounts model = Discounts();
      model.type = selectedDiscountType == "Sales Wise" ? "S" : "P";
      model.mode = selectedDiscountMode == "Auto" ? "A" : "C";
      model.discountOn = selectedDiscountOn == "Percentage" ? "P" : "F";
      model.categoryID = await getCategoryString();
      model.discount = discountController.text.trim();
      model.minDiscount = minLimitController.text.trim();
      model.maxDiscount = maxLimitController.text.trim().isNotEmpty ? maxLimitController.text.trim() : null;
      model.fromDate = fromDate.toString();
      model.toDate = toDate.toString();
      model.formula = await fnUtilities.createDiscountFormula(model);
      model.couponName =
          couponNameController.text.trim().isNotEmpty ? couponNameController.text.trim() : null;
      model.discountActive = 1;
      model.workspaceID = stubs.MyApp.activeWorkspace.workspaceId;
      model.discountSync = 0;

      if (discounts == null) {
        // Creating new discount
        var uuid = const Uuid();
        model.discountID = uuid.v4().toString();
        model.createdBy = stubs.MyApp.activeUser.username;
        model.createdDate = DateTime.now().toString();
        model.rowStatus = 0;

        // Use enhanced DAO for better database operations with proper error handling
        try {
          final database = await AppDatabase().database;
          final enhancedDiscountDao = EnhancedDiscountDao(database);
          await enhancedDiscountDao.insertDiscount(model);
          insertSuccess = true;
          log('✅ Discount inserted successfully using enhanced DAO');
        } catch (e) {
          log('⚠️ Enhanced DAO failed, attempting fallback: $e');
          try {
            await discountDB.insertDiscount(model);
            insertSuccess = true;
            log('✅ Discount inserted successfully using fallback method');
          } catch (fallbackError) {
            log('❌ Both enhanced DAO and fallback failed: $fallbackError');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to save discount: $fallbackError'),
                  backgroundColor: Colors.red,
                ),
              );
            }
            return; // Exit early if both methods fail
          }
        }
      } else {
        // Updating existing discount
        model.discountID = discounts?.discountID;
        model.rowStatus = await commonDB.checkNonSyncCommonFunction(
                    "Discount", "discountID", discounts!.discountID.toString(), "discountSync") ==
                0
            ? 1
            : 0;

        // Use enhanced DAO for better database operations with proper error handling
        try {
          final database = await AppDatabase().database;
          final enhancedDiscountDao = EnhancedDiscountDao(database);
          await enhancedDiscountDao.updateDiscount(model);
          updateSuccess = true;
          log('✅ Discount updated successfully using enhanced DAO');
        } catch (e) {
          log('⚠️ Enhanced DAO failed, attempting fallback: $e');
          try {
            await discountDB.updateDiscount(model);
            updateSuccess = true;
            log('✅ Discount updated successfully using fallback method');
          } catch (fallbackError) {
            log('❌ Both enhanced DAO and fallback failed: $fallbackError');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to update discount: $fallbackError'),
                  backgroundColor: Colors.red,
                ),
              );
            }
            return; // Exit early if both methods fail
          }
        }
      }

      // Only proceed with cleanup and navigation if operation was successful
      if ((discounts == null && insertSuccess) || (discounts != null && updateSuccess)) {
        await clearTextFiled();
        stubs.showToast(
            discounts == null ? "Discount created successfully!" : "Discount updated successfully!");
        if (mounted) {
          Navigator.pop(context);
        }
      } else {
        log('❌ Discount operation failed, not proceeding with cleanup');
      }
    } catch (e) {
      stubs.showToast("Error: ${e.toString()}");
    }
  }

  Future<String> getCategoryString() async {
    // Create a comma-separated string of selected category IDs
    List<String> categoryIds =
        selectedProductKeyList.map((key) => key.productKeyId ?? "").where((id) => id.isNotEmpty).toList();
    return categoryIds.join(",");
  }

  categoryList() {
    var slitCategory = discounts?.categoryID?.split(",");
    for (String s in slitCategory!) {
      try {
        stubs.ProductKey key = productKeyList.firstWhere((element) => element.productKeyId == s);
        productKeyList.firstWhere((element) => element.productKeyId == s).isActive = true;
        key.isActive = true;
        selectedProductKeyList.add(key);
      } catch (ex) {
        // Product key not found in list, skip it
      }
    }
  }

  assignValueForUpdate() async {
    if (discounts != null) {
      if (discounts?.categoryID != null) {
        await categoryList();
      }
      selectedDiscountType = discounts?.type == "S" ? "Sales Wise" : "Product Wise";
      selectedDiscountMode = discounts?.mode == "A" ? "Auto" : "Coupon";
      selectedDiscountOn = discounts?.discountOn == "F" ? "Fixed Amount" : "Percentage";
      discountController.text = discounts?.discount ?? "";
      minLimitController.text = discounts?.minDiscount ?? "";
      maxLimitController.text = discounts?.maxDiscount ?? "";
      fromDate = DateTime.parse(discounts!.fromDate!);
      toDate = DateTime.parse(discounts!.toDate!);
      couponNameController.text = discounts?.couponName ?? "";
    }
  }

  @override
  void initState() {
    queryAllProductKeys();
    super.initState();
  }

  //Category DIALOG
  categoryDialog(BuildContext context) async {
    double width = MediaQuery.of(context).size.width;
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
                contentPadding: const EdgeInsets.all(10),
                scrollable: true,
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: double.maxFinite,
                      height: 250,
                      child: ListView.builder(
                          itemCount: productKeyList.length,
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          itemBuilder: (BuildContext context, int index) {
                            var item = productKeyList[index];
                            return Container(
                              margin: const EdgeInsets.all(0),
                              padding: EdgeInsets.zero,
                              decoration: stubs.boxDecoration(
                                  radius: 10, showShadow: true, bgColor: stubs.secondaryTextColor),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Container(
                                    width: width / 1.5,
                                    child: CheckboxListTile(
                                        dense: false,
                                        controlAffinity: ListTileControlAffinity.leading,
                                        contentPadding: EdgeInsets.zero,
                                        value: item.isActive,
                                        title: Row(
                                          children: [
                                            Container(
                                              width: width / 2,
                                              alignment: Alignment.centerLeft,
                                              child: stubs.TextWidget(item.keywordName ?? "",
                                                  textColor: stubs.primaryTextColor,
                                                  fontSize: stubs.textSizeMedium,
                                                  fontFamily: stubs.fontSemibold,
                                                  isLongText: true),
                                            ),
                                          ],
                                        ),
                                        onChanged: (bool? isValue) {
                                          setState(() {
                                            item.isActive = isValue ?? false;
                                          });
                                        }),
                                  ),
                                ],
                              ),
                            );
                          }),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        GestureDetector(
                          onTap: () {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 3.5,
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                            child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            if (!isLock) {
                              isLock = true;
                              setState(() {
                                selectedProductKeyList.clear();
                                selectedProductKeyList
                                    .addAll(productKeyList.where((element) => element.isActive == true));
                                if (selectedProductKeyList.isEmpty) {
                                  stubs.showToast("Please select category");
                                }
                              });
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 3.5,
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                            child: TextWidget("Ok", textColor: secondaryTextColor, isCentered: true),
                          ),
                        ),
                      ],
                    ),
                  ],
                ));
          });
        });
  }

  Widget _buildDropdownCard(String title, String? value, List<String> items, Function(String?) onChanged) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              icon: const Icon(Icons.keyboard_arrow_down),
              items: items.map((String item) {
                return DropdownMenuItem<String>(
                  value: item,
                  child: Text(item),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputCard(String title, TextEditingController controller,
      {String? suffix, TextInputType? keyboardType, String? Function(String?)? validator}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              controller: controller,
              keyboardType: keyboardType ?? TextInputType.text,
              validator: validator,
              decoration: InputDecoration(
                labelText: title,
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          if (suffix != null)
            Text(
              suffix,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDateCard(String date, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.0),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              date,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Icon(Icons.calendar_today, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const CommonDrawer(),
      backgroundColor: Colors.grey[100],
      appBar: CommonAppBar(title: discounts == null ? "Create Discount" : "Update Discount"),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: validator.formkey,
          child: SingleChildScrollView(
            child: Column(children: [
              // Discount Type
              _buildDropdownCard(
                'Discount Type',
                selectedDiscountType,
                ['Sales Wise', 'Product Wise'],
                (String? value) async {
                  if (!isLock) {
                    isLock = true;
                    setState(() {
                      selectedDiscountType = value;
                      if (selectedDiscountType == "Sales Wise") {
                        selectedProductKeyList.clear();
                      }
                    });
                    isLock = false;
                  }
                },
              ),
              const SizedBox(height: 16),

              // Discount Mode
              if (selectedDiscountType != null) ...[
                _buildDropdownCard(
                  'Discount Mode',
                  selectedDiscountMode,
                  ['Auto', 'Coupon'],
                  (String? value) async {
                    if (!isLock) {
                      isLock = true;
                      setState(() {
                        selectedDiscountMode = value;
                      });
                      isLock = false;
                    }
                  },
                ),
                const SizedBox(height: 16),
              ],

              // Discount On
              if (selectedDiscountMode != null) ...[
                _buildDropdownCard(
                  'Discount On',
                  selectedDiscountOn,
                  ['Percentage', 'Fixed Amount'],
                  (String? value) async {
                    if (!isLock) {
                      isLock = true;
                      setState(() {
                        selectedDiscountOn = value;
                      });
                      isLock = false;
                    }
                  },
                ),
                const SizedBox(height: 16),

                // Form Fields
                if (selectedDiscountOn != null) ...[
                  // Coupon Name (if Coupon mode)
                  if (selectedDiscountMode == "Coupon") ...[
                    _buildInputCard(
                      "Coupon Name",
                      couponNameController,
                      validator: validator.validateTextField,
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Category Selection (if Product Wise)
                  if (selectedDiscountType == "Product Wise") ...[
                    GestureDetector(
                      onTap: () async {
                        FocusScope.of(context).requestFocus(FocusNode());
                        await categoryDialog(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8.0),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              spreadRadius: 1,
                              blurRadius: 3,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              selectedProductKeyList.isEmpty
                                  ? "Select Category"
                                  : "${selectedProductKeyList.length} categories selected",
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Amount/Percentage Input
                  _buildInputCard(
                    selectedDiscountOn == "Percentage" ? "Percentage (%)" : "Amount (Rs)",
                    discountController,
                    suffix: selectedDiscountOn == "Percentage" ? "Percentage" : "Rupees",
                    keyboardType: TextInputType.number,
                    validator: validator.validateAmount,
                  ),
                  const SizedBox(height: 16),

                  // Min and Max Limit
                  Row(
                    children: [
                      Expanded(
                        child: _buildInputCard(
                          "Min Limit (Rs)",
                          minLimitController,
                          keyboardType: TextInputType.number,
                          validator: validator.validateMinValue,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: selectedDiscountType == "Sales Wise" && selectedDiscountOn == "Fixed Amount"
                            ? Container()
                            : _buildInputCard(
                                "Max Limit (Rs)",
                                maxLimitController,
                                keyboardType: TextInputType.number,
                                validator: (String? value) {
                                  if (value != null && value.isNotEmpty) {
                                    if ((int.tryParse(value) ?? 0) <= 0) {
                                      return "Max must be greater than zero";
                                    } else if ((int.tryParse(value) ?? 0) <=
                                        (int.tryParse(minLimitController.text) ?? 0)) {
                                      return "Max limit must be greater than min limit";
                                    }
                                  }
                                  return null;
                                },
                              ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Date Selection
                  Row(
                    children: [
                      Expanded(
                        child: _buildDateCard(
                          fnUtilities.convertDate(fromDate.toString()),
                          () => selectDate(context, 'fromDate'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildDateCard(
                          fnUtilities.convertDate(toDate.toString()),
                          () => selectDate(context, 'toDate'),
                        ),
                      ),
                    ],
                  ),

                  // Create Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () async {
                        if (!isLock) {
                          isLock = true;

                          // Validate required fields
                          if (selectedDiscountType == null) {
                            stubs.showToast("Please select discount type");
                            isLock = false;
                            return;
                          }

                          if (selectedDiscountMode == null) {
                            stubs.showToast("Please select discount mode");
                            isLock = false;
                            return;
                          }

                          if (selectedDiscountOn == null) {
                            stubs.showToast("Please select discount on");
                            isLock = false;
                            return;
                          }

                          if (selectedDiscountType == "Product Wise" && selectedProductKeyList.isEmpty) {
                            stubs.showToast("Please select at least one category");
                            isLock = false;
                            return;
                          }

                          if (selectedDiscountMode == "Coupon" && couponNameController.text.trim().isEmpty) {
                            stubs.showToast("Please enter coupon name");
                            isLock = false;
                            return;
                          }

                          if (discountController.text.trim().isEmpty) {
                            stubs.showToast("Please enter discount amount/percentage");
                            isLock = false;
                            return;
                          }

                          if (minLimitController.text.trim().isEmpty) {
                            stubs.showToast("Please enter minimum limit");
                            isLock = false;
                            return;
                          }

                          if (validator.validate() ?? false) {
                            try {
                              await onPressDiscount();
                            } catch (e) {
                              stubs.showToast("Error creating discount: ${e.toString()}");
                            }
                          } else {
                            stubs.showToast("Please fill all required fields correctly");
                          }

                          isLock = false;
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        discounts == null ? "Create Discount" : "Update Discount",
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ]
              ]
            ]),
          ),
        ),
      ),
    );
  }
}
