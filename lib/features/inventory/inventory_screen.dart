import 'package:coffee_cofe/core/styles.dart';
import 'package:coffee_cofe/features/inventory/inventory_screen_provider.dart';
import 'package:coffee_cofe/features/profile/profile_settings_controller.dart';
import 'package:coffee_cofe/widgets/common_appbar.dart';
import 'package:coffee_cofe/widgets/common_drawer.dart';
import 'package:coffee_cofe/widgets/input_form_field.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../widgets/common_dropdown.dart';
import '../../widgets/label_with_widget.dart';

class InventoryScreen extends StatefulWidget {
  const InventoryScreen({super.key});

  @override
  State<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends State<InventoryScreen> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 500), () {
      initFunction();
    });
  }

  initFunction() async {
    await Provider.of<InventoryScreenProvider>(context, listen: false).initFunction();
    setState(() {}); // Ensure the UI is refreshed after initial data loading
  }

  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = ''; // Store the search query

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<InventoryScreenProvider>(context);

    return ValueListenableBuilder<Color>(
        valueListenable: ProfileController.themeColorNotifier,
        builder: (context, bgColor, child) {
          return Scaffold(
              drawer: const CommonDrawer(),
              floatingActionButton: Padding(
                padding: const EdgeInsets.only(left: 10.0, bottom: 25, right: 25),
                child: FloatingActionButton(
                  backgroundColor: bgColor,
                  onPressed: () {
                    addInventoryProduct(provider);
                  },
                  child: const Icon(
                    Icons.add,
                    size: 35,
                    color: Colors.white,
                  ),
                ),
              ),
              appBar: CommonAppBar(
                title: 'Inventory',
                appBarColor: bgColor,
              ),
              body: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    InputFormField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search Inventory...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value.toLowerCase(); // Update the search query
                        });
                      },
                    ),
                    Flexible(
                      child: ListView.builder(
                        itemCount: provider.filteredInventoryList(_searchQuery).length,
                        itemBuilder: (context, index) {
                          final item = provider.filteredInventoryList(_searchQuery)[index];

                          // Parse quantity to double safely
                          double qty = double.tryParse(item.inventoryQunatity ?? "0") ?? 0;

                          bool isLowStock = false;
                          if (item.inventoryUnit == "Kg" || item.inventoryUnit == "Litres") {
                            isLowStock = qty < 1;
                          } else if (item.inventoryUnit == "No" || item.inventoryUnit == "Pack") {
                            isLowStock = qty < 5;
                          }

                          return Container(
                            margin: const EdgeInsets.symmetric(vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.2),
                                  blurRadius: 6,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: Stack(
                              children: [
                                ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: isLowStock ? Colors.redAccent : Colors.blueAccent,
                                    child: Text(
                                      item.inventoyProductName?.substring(0, 1).toUpperCase() ?? '?',
                                      style: const TextStyle(color: Colors.white),
                                    ),
                                  ),
                                  title: Text(
                                    item.inventoyProductName ?? "",
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                  subtitle: Text(
                                    'Quantity: ${double.tryParse(item.inventoryQunatity ?? "0")?.toStringAsFixed(2)} ${item.inventoryUnit ?? ""}',
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                  trailing: IconButton(
                                    icon: const Icon(Icons.edit, color: Colors.grey),
                                    onPressed: () async {
                                      await provider.oneEdit(item); // ✅ Mark it as editing

                                      await addInventoryProduct(provider); // Open your bottomsheet
                                      await provider.loadInventories(); // Reload the latest data
                                      setState(() {}); // Refresh the UI after the data has been loaded
                                    },
                                  ),
                                ),

                                // Low stock indicator if stock is low
                                if (isLowStock)
                                  Positioned(
                                    right: 75,
                                    top: 25,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.redAccent,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: const Text(
                                        'Low Stock',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ));
        });
  }

  addInventoryProduct(InventoryScreenProvider provider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Allow custom height
      showDragHandle: true,
      useSafeArea: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, setState) {
            return Padding(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 20, // Adjust based on keyboard height
              ),
              child: SizedBox(
                height: MediaQuery.of(context).size.height - 50, // Full screen height
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      // Title Section
                      Align(
                        alignment: Alignment.topLeft,
                        child: Text(
                          provider.selectedInventory == null ? 'Add Inventory' : 'Update Inventory',
                          style: black22w500,
                          textAlign: TextAlign.left,
                        ),
                      ),
                      const SizedBox(height: 20), // Space between title and form

                      // Form fields
                      LabeledFormField(
                        "Product Name *",
                        InputFormField(
                          autoFocus: true,
                          hint: "Enter product name",
                          controller: provider.nameController,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          Expanded(
                            child: LabeledFormField(
                              "Quantity",
                              InputFormField(
                                hint: "Enter quantity",
                                controller: provider.quantityController,
                                inputType: const TextInputType.numberWithOptions(decimal: true),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: SizedBox(
                              width: 140,
                              child: LabeledFormField(
                                "Unit",
                                CommonDropdown(
                                  enableMargine: false,
                                  items: provider.units,
                                  hitLabel: "Select Unit",
                                  selectedItem: provider.selectedUnit,
                                  onChanged: (value) async {
                                    if (value != null) {
                                      provider.onChageunit(value);
                                    }
                                    setState(() {}); // Trigger a UI update
                                  },
                                  isSearchable: false,
                                  // itemLabel: (DropdownItem item) => item.label,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      ElevatedButton(
                        onPressed: () async {
                          if (provider.nameController.text.isNotEmpty) {
                            final navigator = Navigator.of(context);
                            final scaffoldMessenger = ScaffoldMessenger.of(context);
                            try {
                              final success = await provider.saveCategory();
                              if (success && mounted) {
                                navigator.pop();

                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      provider.selectedInventory == null
                                          ? 'Inventory added successfully!'
                                          : 'Inventory updated successfully!',
                                    ),
                                  ),
                                );

                                // Refresh the inventory list immediately after adding or updating
                                await provider.loadInventories();
                                setState(() {}); // Trigger UI update to reflect the latest data
                              } else if (!success && mounted) {
                                scaffoldMessenger.showSnackBar(
                                  const SnackBar(
                                    content: Text('Failed to save inventory. Please try again!'),
                                  ),
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text('Error occurred: ${e.toString()}'),
                                  ),
                                );
                              }
                            } finally {
                              provider.clearCategoryForm(); // Clear form after the operation
                            }
                          }
                        },
                        child:
                            Text(provider.selectedInventory == null ? 'Add Inventory' : 'Update Inventory'),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    ).whenComplete(() async {
      await provider.clearCategoryForm();
    });
  }
}
