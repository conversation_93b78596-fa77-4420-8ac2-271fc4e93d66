import 'dart:developer';
import 'package:flutter/material.dart';
import 'database/immediate_fix.dart';

/// Simple app to fix database issues
/// Run this with: flutter run lib/main_database_fix.dart
void main() {
  runApp(const DatabaseFixApp());
}

class DatabaseFixApp extends StatelessWidget {
  const DatabaseFixApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'Database Fix Utility',
      home: DatabaseFixScreen(),
    );
  }
}

class DatabaseFixScreen extends StatefulWidget {
  const DatabaseFixScreen({super.key});

  @override
  State<DatabaseFixScreen> createState() => _DatabaseFixScreenState();
}

class _DatabaseFixScreenState extends State<DatabaseFixScreen> {
  bool _isFixing = false;
  String _status = 'Ready to fix database issues';
  List<String> _logs = [];

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
    log(message);
  }

  Future<void> _runQuickFix() async {
    setState(() {
      _isFixing = true;
      _status = 'Running database repair...';
      _logs.clear();
    });

    try {
      _addLog('🔧 Starting immediate database fix...');

      await ImmediateDatabaseFix.fixNow();

      _addLog('🎉 Database repair completed successfully!');
      setState(() {
        _status = 'Database repair completed successfully!';
      });
    } catch (e) {
      _addLog('❌ Database repair failed: $e');
      setState(() {
        _status = 'Database repair failed: $e';
      });
    } finally {
      setState(() {
        _isFixing = false;
      });
    }
  }

  Future<void> _fixSalesTable() async {
    setState(() {
      _isFixing = true;
      _status = 'Fixing Sales table...';
    });

    try {
      await ImmediateDatabaseFix.fixNow();
      _addLog('✅ Sales table fixed');
      setState(() {
        _status = 'Sales table fixed successfully';
      });
    } catch (e) {
      _addLog('❌ Failed to fix Sales table: $e');
      setState(() {
        _status = 'Failed to fix Sales table: $e';
      });
    } finally {
      setState(() {
        _isFixing = false;
      });
    }
  }

  Future<void> _fixDiscountTable() async {
    setState(() {
      _isFixing = true;
      _status = 'Fixing Discount table...';
    });

    try {
      await ImmediateDatabaseFix.fixNow();
      _addLog('✅ Discount table fixed');
      setState(() {
        _status = 'Discount table fixed successfully';
      });
    } catch (e) {
      _addLog('❌ Failed to fix Discount table: $e');
      setState(() {
        _status = 'Failed to fix Discount table: $e';
      });
    } finally {
      setState(() {
        _isFixing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Database Fix Utility'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Database Status',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(_status),
                    if (_isFixing) ...[
                      SizedBox(height: 8),
                      LinearProgressIndicator(),
                    ],
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isFixing ? null : _runQuickFix,
              child: Text('🔧 Run Complete Database Fix'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isFixing ? null : _fixSalesTable,
                    child: Text('Fix Sales Table'),
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isFixing ? null : _fixDiscountTable,
                    child: Text('Fix Discount Table'),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            Text(
              'Logs',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: ListView.builder(
                  itemCount: _logs.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      child: Text(
                        _logs[index],
                        style: TextStyle(fontSize: 12, fontFamily: 'monospace'),
                      ),
                    );
                  },
                ),
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Instructions:\n'
              '1. Click "Run Complete Database Fix" to fix all issues\n'
              '2. Or use individual table fix buttons\n'
              '3. Check logs for detailed information\n'
              '4. Restart your main app after fixing',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }
}
